definitions:
  domain.CreateOrderRequest:
    properties:
      currency:
        example: USD
        type: string
      payed_method:
        example: stripe
        type: string
      price_id:
        example: price_456
        type: string
      product_desc:
        example: Premium Subscription
        type: string
      product_id:
        example: prod_123
        type: string
      psp_provider:
        example: stripe
        type: string
      quantity:
        example: 1
        minimum: 1
        type: integer
    required:
    - price_id
    - product_id
    - psp_provider
    - quantity
    type: object
  domain.CreateOrderResponse:
    properties:
      amount:
        example: 99.99
        type: number
      checkout_url:
        example: https://mock-payment.example.com/stripe/checkout/...
        type: string
      currency:
        example: USD
        type: string
      expires_at:
        example: "2025-07-11T15:30:45Z"
        type: string
      order_id:
        example: 20250710153045999stripe1234567890123456789
        type: string
    type: object
  domain.ErrorResponse:
    properties:
      code:
        example: INVALID_REQUEST
        type: string
      message:
        example: 'Missing required field: product_id'
        type: string
    type: object
info:
  contact:
    email: <EMAIL>
    name: wenchao
    url: xxx
  description: 支付后端服务API文档，提供订单管理、支付处理等功能
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Payment Backend API
  version: "1"
paths:
  /api/v1/order-service/orders:
    post:
      consumes:
      - application/json
      description: 创建新的订单并生成支付链接，需要用户认证
      parameters:
      - description: 用户ID
        example: '"user123"'
        in: header
        name: x-user-id
        required: true
        type: string
      - description: 用户角色
        example: '"customer"'
        in: header
        name: x-role
        required: true
        type: string
      - description: 创建订单请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateOrderRequest'
      produces:
      - application/json
      responses:
        "303":
          description: 订单创建成功，返回支付链接
          schema:
            $ref: '#/definitions/domain.CreateOrderResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/domain.ErrorResponse'
        "401":
          description: 未授权，缺少认证信息
          schema:
            $ref: '#/definitions/domain.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/domain.ErrorResponse'
      summary: 创建订单
      tags:
      - 订单管理
swagger: "2.0"
