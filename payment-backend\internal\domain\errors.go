package domain

// 常用错误代码
const (
	ErrCodeInvalidRequest     = "INVALID_REQUEST"
	ErrCodePaymentNotFound    = "PAYMENT_NOT_FOUND"
	ErrCodeSessionNotFound    = "SESSION_NOT_FOUND"
	ErrCodeInvalidProvider    = "INVALID_PROVIDER"
	ErrCodeSessionExpired     = "SESSION_EXPIRED"
	ErrCodePaymentCompleted   = "PAYMENT_COMPLETED"
	ErrCodePaymentCancelled   = "PAYMENT_CANCELLED"
	ErrCodeInsufficientFunds  = "INSUFFICIENT_FUNDS"
	ErrCodeInvalidSignature   = "INVALID_SIGNATURE"
	ErrCodeCreateOrderFailed  = "CREATE_ORDER_FAILED"
	ErrCodeWebhookFailed      = "WEBHOOK_FAILED"
	ErrCodeGatewayUnavailable = "GATEWAY_UNAVAILABLE"
	ErrCodeGatewayTimeout     = "GATEWAY_TIMEOUT"
	ErrCodeRefundFailed       = "REFUND_FAILED"
	ErrCodeRefundNotAllowed   = "REFUND_NOT_ALLOWED"
	ErrCodeDuplicatePayment   = "DUPLICATE_PAYMENT"
	ErrCodeDuplicateSession   = "DUPLICATE_SESSION"
	ErrCodeInternalError      = "INTERNAL_ERROR"
)

const (
	MsgUserContext    = "Invalid user context."
	MsgInvalidRequest = "Invalid request."
)

// ErrorResponse 通用错误响应
type ErrorResponse struct {
	Code    string `json:"code,omitempty" example:"INVALID_REQUEST" comment:"错误代码"`
	Message string `json:"message,omitempty" example:"Missing required field: product_id" comment:"详细错误信息"`
}

// NewErrorResponse 创建通用错误
func NewErrorResponse(code, message string) *ErrorResponse {
	return &ErrorResponse{
		Code:    code,
		Message: message,
	}
}
