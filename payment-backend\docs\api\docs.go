// Package api Code generated by swaggo/swag. DO NOT EDIT
package api

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "wenchao",
            "url": "xxx",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/order-service/orders": {
            "post": {
                "description": "创建新的订单并生成支付链接，需要用户认证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单管理"
                ],
                "summary": "创建订单",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"user123\"",
                        "description": "用户ID",
                        "name": "x-user-id",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"customer\"",
                        "description": "用户角色",
                        "name": "x-role",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "创建订单请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.CreateOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "303": {
                        "description": "订单创建成功，返回支付链接",
                        "schema": {
                            "$ref": "#/definitions/domain.CreateOrderResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/domain.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，缺少认证信息",
                        "schema": {
                            "$ref": "#/definitions/domain.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/domain.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "domain.CreateOrderRequest": {
            "type": "object",
            "required": [
                "price_id",
                "product_id",
                "psp_provider",
                "quantity"
            ],
            "properties": {
                "currency": {
                    "type": "string",
                    "example": "USD"
                },
                "payed_method": {
                    "type": "string",
                    "example": "stripe"
                },
                "price_id": {
                    "type": "string",
                    "example": "price_456"
                },
                "product_desc": {
                    "type": "string",
                    "example": "Premium Subscription"
                },
                "product_id": {
                    "type": "string",
                    "example": "prod_123"
                },
                "psp_provider": {
                    "type": "string",
                    "example": "stripe"
                },
                "quantity": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                }
            }
        },
        "domain.CreateOrderResponse": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "number",
                    "example": 99.99
                },
                "checkout_url": {
                    "type": "string",
                    "example": "https://mock-payment.example.com/stripe/checkout/..."
                },
                "currency": {
                    "type": "string",
                    "example": "USD"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-07-11T15:30:45Z"
                },
                "order_id": {
                    "type": "string",
                    "example": "20250710153045999stripe1234567890123456789"
                }
            }
        },
        "domain.ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "example": "INVALID_REQUEST"
                },
                "message": {
                    "type": "string",
                    "example": "Missing required field: product_id"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "Payment Backend API",
	Description:      "支付后端服务API文档，提供订单管理、支付处理等功能",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
