package router

import (
	"github.com/gin-gonic/gin"

	"payment-backend/internal/handler"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"

	// docs are generated by Swag CLI, you have to import it.
	docs "payment-backend/docs/api"

	swaggerfiles "github.com/swaggo/files"     // swagger embed files
	ginSwagger "github.com/swaggo/gin-swagger" // gin-swagger middleware
)

// SetupRoutes 设置路由
func SetupRoutes(
	router *gin.Engine,
	orderHandler *handler.OrderHandler,
	logger logger.Logger,
) *gin.Engine {

	// 全局中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 健康检查（无需认证）
	router.GET("/health", orderHandler.HealthCheck)

	if gin.Mode() == gin.DebugMode {

		docs.SwaggerInfo.BasePath = "/api/v1" // 设置路径
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))
	}

	// API v1 路由组
	v1 := router.Group("/api/v1")
	{
		// 订单服务路由组
		orderService := v1.Group("/order-service")
		{
			// 需要认证的路由
			authenticated := orderService.Group("")
			authenticated.Use(middleware.AuthMiddleware(logger))
			{
				// 订单管理 - 需要认证
				authenticated.POST("/orders", orderHandler.CreateOrder)
				authenticated.GET("/orders/:order_id", orderHandler.GetOrder)
				authenticated.GET("/orders/id/:id", orderHandler.GetOrderByID)
				authenticated.GET("/orders", orderHandler.GetUserOrders)
				authenticated.PUT("/orders/:order_id", orderHandler.UpdateOrder)
				authenticated.POST("/orders/:order_id/cancel", orderHandler.CancelOrder)
				authenticated.POST("/orders/:order_id/refund", orderHandler.RefundOrder)
			}

			// 管理员路由 - 需要管理员权限
			admin := orderService.Group("/admin")
			admin.Use(middleware.AuthMiddleware(logger))
			admin.Use(middleware.RequireRole("admin", "super_admin"))
			{
				// 这里可以添加管理员专用的接口
				admin.GET("/orders", orderHandler.ListAllOrders)
				admin.POST("/orders/:order_id/force-refund", orderHandler.ForceRefund)
			}

			// Webhook - 可选认证（外部调用）
			webhooks := orderService.Group("/webhooks")
			webhooks.Use(middleware.OptionalAuthMiddleware(logger))
			{
				webhooks.POST("/stripe", orderHandler.ProcessWebhookStripe)
			}
		}
	}

	return router
}

// SetupTestRoutes 设置测试路由（用于单元测试）
func SetupTestRoutes(orderHandler *handler.OrderHandler) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 健康检查
	router.GET("/health", orderHandler.HealthCheck)

	// 测试路由（不使用中间件）
	router.POST("/orders", orderHandler.CreateOrder)
	router.GET("/orders/:order_id", orderHandler.GetOrder)
	router.GET("/orders/id/:id", orderHandler.GetOrderByID)
	router.GET("/orders", orderHandler.GetUserOrders)
	router.PUT("/orders/:order_id", orderHandler.UpdateOrder)
	router.POST("/orders/:order_id/cancel", orderHandler.CancelOrder)
	router.POST("/orders/:order_id/refund", orderHandler.RefundOrder)
	router.POST("/webhooks/stripe", orderHandler.ProcessWebhookStripe)

	return router
}
