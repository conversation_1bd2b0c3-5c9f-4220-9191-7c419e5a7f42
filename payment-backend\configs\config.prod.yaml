# 生产环境配置

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30
  write_timeout: 30
  mode: "release"

# 数据库配置（生产环境使用MySQL）
database:
  driver: "mysql"
  host: "mysql-server"
  port: 3306
  username: "payment_user"
  password: "payment_password"
  database: "payment_db"
  ssl_mode: "disable"

# 支付配置
payment:
  providers:
    stripe:
      secret_key: ""
      webhook_secret: ""
      api_version: "2023-10-16"
    paypal:
      client_id: ""
      client_secret: ""
      webhook_id: ""
      environment: "production" # sandbox or production

# 日志配置
log:
  level: "info"
  format: "json"
  output: "stdout"

# 雪花算法配置
snowflake:
  node_id: -1
